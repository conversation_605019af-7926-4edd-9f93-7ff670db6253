require('dotenv').config();
const { ethers } = require('ethers');

// Standard ERC-20 ABI for balanceOf function
const ERC20_ABI = [
  "function balanceOf(address owner) view returns (uint256)"
];

/**
 * Execute the token authority gate kernel
 * @param {Object} inputs - The input parameters
 * @param {string} inputs.userAddress - Ethereum address to check balance for
 * @param {string} inputs.tokenAddress - ERC-20 token contract address
 * @param {string} inputs.minBalance - Minimum required balance (in wei/smallest unit)
 * @returns {Promise<Object>} - Authorization result with balance information
 */
async function execute(inputs) {
  try {
    // Validate inputs
    if (!inputs.userAddress || !inputs.tokenAddress || !inputs.minBalance) {
      throw new Error('Missing required inputs: userAddress, tokenAddress, and minBalance are required');
    }

    // Load RPC URL from environment
    const rpcUrl = process.env.RPC_URL;
    if (!rpcUrl) {
      throw new Error('RPC_URL environment variable is required');
    }

    // Validate Ethereum addresses
    if (!ethers.isAddress(inputs.userAddress)) {
      throw new Error('Invalid userAddress: must be a valid Ethereum address');
    }

    if (!ethers.isAddress(inputs.tokenAddress)) {
      throw new Error('Invalid tokenAddress: must be a valid Ethereum address');
    }

    // Validate minBalance is a valid number
    let minBalanceBigInt;
    try {
      minBalanceBigInt = BigInt(inputs.minBalance);
    } catch (error) {
      throw new Error('Invalid minBalance: must be a valid integer string');
    }

    // Create provider and contract instance
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const tokenContract = new ethers.Contract(inputs.tokenAddress, ERC20_ABI, provider);

    // Get the user's token balance
    const actualBalance = await tokenContract.balanceOf(inputs.userAddress);

    // Check if user meets minimum balance requirement
    const authorized = actualBalance >= minBalanceBigInt;

    // Return the result
    return {
      authorized: authorized,
      actualBalance: actualBalance.toString()
    };

  } catch (error) {
    // Handle different types of errors
    if (error.code === 'NETWORK_ERROR' || error.code === 'SERVER_ERROR') {
      throw new Error(`Network error: Unable to connect to Ethereum RPC. ${error.message}`);
    } else if (error.code === 'CALL_EXCEPTION') {
      throw new Error(`Contract call failed: Invalid token contract or network issue. ${error.message}`);
    } else if (error.message.includes('Invalid')) {
      // Re-throw validation errors as-is
      throw error;
    } else {
      // Generic error handling
      throw new Error(`Execution failed: ${error.message}`);
    }
  }
}

module.exports = { execute };
