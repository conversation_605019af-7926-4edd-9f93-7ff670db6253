require('dotenv').config();
const { execute } = require('./index.js');

async function runSmokeTest() {
  console.log('🧪 Running Token Authority Gate Smoke Test...\n');

  // Test configuration
  const testCases = [
    {
      name: 'USDT Balance Check - High Threshold',
      inputs: {
        userAddress: '******************************************', // <PERSON><PERSON>'s address
        tokenAddress: '******************************************', // USDT on Ethereum
        minBalance: '1000000000' // 1000 USDT (6 decimals)
      }
    },
    {
      name: 'USDT Balance Check - Low Threshold',
      inputs: {
        userAddress: '******************************************', // <PERSON><PERSON>'s address
        tokenAddress: '******************************************', // USDT on Ethereum
        minBalance: '1000000' // 1 USDT (6 decimals)
      }
    }
  ];

  // Check if RPC_URL is configured
  if (!process.env.RPC_URL) {
    console.error('❌ Error: RPC_URL environment variable not set');
    console.log('Please copy .env.example to .env and configure your RPC URL');
    process.exit(1);
  }

  console.log(`🔗 Using RPC: ${process.env.RPC_URL.substring(0, 50)}...`);
  console.log('');

  // Run test cases
  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`📋 Test ${i + 1}: ${testCase.name}`);
    console.log(`   User Address: ${testCase.inputs.userAddress}`);
    console.log(`   Token Address: ${testCase.inputs.tokenAddress}`);
    console.log(`   Min Balance: ${testCase.inputs.minBalance}`);

    try {
      const startTime = Date.now();
      const result = await execute(testCase.inputs);
      const duration = Date.now() - startTime;

      console.log(`   ✅ Success (${duration}ms)`);
      console.log(`   📊 Result:`);
      console.log(`      Authorized: ${result.authorized}`);
      console.log(`      Actual Balance: ${result.actualBalance}`);
      
      // Validate result structure
      if (typeof result.authorized !== 'boolean') {
        throw new Error('Invalid result: authorized must be boolean');
      }
      if (typeof result.actualBalance !== 'string') {
        throw new Error('Invalid result: actualBalance must be string');
      }

      // Convert balance to human readable format (assuming 6 decimals for USDT)
      const balanceInTokens = (BigInt(result.actualBalance) / BigInt(1000000)).toString();
      console.log(`      Human Readable: ${balanceInTokens} USDT`);

    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
    }
    
    console.log('');
  }

  // Test error cases
  console.log('🔍 Testing Error Cases...\n');

  const errorTestCases = [
    {
      name: 'Invalid User Address',
      inputs: {
        userAddress: 'invalid-address',
        tokenAddress: '******************************************',
        minBalance: '1000000'
      },
      expectedError: 'Invalid userAddress'
    },
    {
      name: 'Missing Required Input',
      inputs: {
        userAddress: '******************************************',
        tokenAddress: '******************************************'
        // minBalance missing
      },
      expectedError: 'Missing required inputs'
    }
  ];

  for (let i = 0; i < errorTestCases.length; i++) {
    const testCase = errorTestCases[i];
    console.log(`🚫 Error Test ${i + 1}: ${testCase.name}`);

    try {
      await execute(testCase.inputs);
      console.log(`   ❌ Expected error but got success`);
    } catch (error) {
      if (error.message.includes(testCase.expectedError)) {
        console.log(`   ✅ Correctly caught expected error: ${error.message}`);
      } else {
        console.log(`   ⚠️  Caught unexpected error: ${error.message}`);
      }
    }
    console.log('');
  }

  console.log('🎉 Smoke test completed!');
}

// Run the smoke test
if (require.main === module) {
  runSmokeTest().catch(console.error);
}

module.exports = { runSmokeTest };
