id: token_authority_gate
version: 0.1.0
inputs:
  - name: userAddress
    type: string
    description: "Ethereum address to check balance for"
  - name: tokenAddress
    type: string
    description: "ERC-20 token contract address"
  - name: minBalance
    type: uint256
    description: "Minimum required balance (in wei/smallest unit)"
outputs:
  - name: authorized
    type: bool
    description: "Whether user meets minimum balance requirement"
  - name: actualBalance
    type: uint256
    description: "User's actual token balance as string"
